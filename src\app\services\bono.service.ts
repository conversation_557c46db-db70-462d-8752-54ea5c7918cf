import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface Bono {
  id_bono?: number;
  id_personal: number;
  tipo_bono: string;
  descripcion: string;
  monto: number;
  fecha_otorgamiento: string;
  fecha_pago?: string;
  estado: string;
  observaciones?: string;
  fecha_creacion?: string;
  fecha_actualizacion?: string;
}

export interface ResumenBonos {
  id_personal: number;
  anio: number;
  total_bonos: number;
  monto_total: number;
  bonos_pagados: number;
  bonos_pendientes: number;
  monto_pagado: number;
  monto_pendiente: number;
}

@Injectable({
  providedIn: 'root'
})
export class BonoService {
  private apiUrl = `${environment.apiUrl}/bonos`;

  constructor(private http: HttpClient) { }

  private getHttpOptions() {
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };
  }

  // Listar bonos por personal
  listarBonosPorPersonal(idPersonal: number): Observable<Bono[]> {
    return this.http.get<Bono[]>(`${this.apiUrl}/personal/${idPersonal}/`, this.getHttpOptions());
  }

  // Listar bonos por personal y rango de fechas
  listarBonosPorPersonalFechas(idPersonal: number, fechaInicio: string, fechaFin: string): Observable<Bono[]> {
    return this.http.get<Bono[]>(`${this.apiUrl}/personal/${idPersonal}/fechas/${fechaInicio}/${fechaFin}/`, this.getHttpOptions());
  }

  // Obtener bono por ID
  obtenerBonoPorId(idBono: number): Observable<Bono> {
    return this.http.get<Bono>(`${this.apiUrl}/${idBono}/`, this.getHttpOptions());
  }

  // Crear bono
  crearBono(bono: Bono): Observable<Bono> {
    return this.http.post<Bono>(`${this.apiUrl}/crear/`, bono, this.getHttpOptions());
  }

  // Actualizar bono
  actualizarBono(idBono: number, bono: Bono): Observable<Bono> {
    return this.http.put<Bono>(`${this.apiUrl}/actualizar/${idBono}/`, bono, this.getHttpOptions());
  }

  // Eliminar bono
  eliminarBono(idBono: number): Observable<any> {
    return this.http.delete(`${this.apiUrl}/eliminar/${idBono}/`, this.getHttpOptions());
  }

  // Obtener resumen de bonos por personal y año
  resumenBonosPersonal(idPersonal: number, anio: number): Observable<ResumenBonos> {
    return this.http.get<ResumenBonos>(`${this.apiUrl}/resumen/${idPersonal}/anio/${anio}/`, this.getHttpOptions());
  }
}
